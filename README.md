# QB-MDT - Mobile Data Terminal

A comprehensive Mobile Data Terminal system for QBCore FiveM servers, designed specifically for police departments and law enforcement roleplay.

## Features

### 🚔 Core Functionality
- **Citizen Search**: Search for citizens by name or citizen ID with complete criminal history
- **Vehicle Search**: Look up vehicle information by license plate
- **Report System**: Create, view, and manage incident reports
- **BOLO System**: Create and manage "Be On the Lookout" alerts
- **Criminal Records**: Track charges, fines, and jail time
- **Evidence Management**: Document and track evidence for cases

### 🎨 User Interface
- Modern, responsive design with dark theme
- Intuitive navigation with tabbed interface
- Real-time search results
- Modal dialogs for data entry
- Mobile-friendly responsive layout

### 🔒 Security & Permissions
- Job-based access control
- Configurable permissions system
- Secure database queries with prepared statements
- Input validation and sanitization

## Installation

### Prerequisites
- QBCore Framework
- oxmysql resource
- MySQL/MariaDB database

### Step 1: Download and Install
1. Download the `qb-mdt` resource
2. Place it in your `resources/[qb]/[police]/` folder
3. Add `ensure qb-mdt` to your `server.cfg`

### Step 2: Database Setup
1. Import the `sql.sql` file into your database
2. This will create all necessary tables and indexes

### Step 3: Configuration
1. Edit `config.lua` to match your server setup
2. Configure police job names, MDT locations, and permissions
3. Customize charges and evidence types as needed

### Step 4: Dependencies
Make sure you have these resources installed and started:
- `qb-core`
- `oxmysql`

## Configuration

### Police Jobs
Edit the `Config.PoliceJobs` table in `config.lua`:
```lua
Config.PoliceJobs = {
    'police',
    'sheriff',
    'statepolice'
}
```

### MDT Locations
Add or modify MDT access points:
```lua
Config.MDTLocations = {
    {
        coords = vector3(441.07, -979.56, 30.69),
        heading = 180.0,
        width = 2.0,
        height = 2.0,
        minZ = 29.69,
        maxZ = 31.69
    }
}
```

### Permissions
Configure who can access different features:
```lua
Config.Permissions = {
    ['citizen_search'] = {'police', 'sheriff', 'statepolice'},
    ['vehicle_search'] = {'police', 'sheriff', 'statepolice'},
    ['create_report'] = {'police', 'sheriff', 'statepolice'},
    ['view_reports'] = {'police', 'sheriff', 'statepolice'},
    ['create_bolo'] = {'police', 'sheriff', 'statepolice'},
    ['admin_panel'] = {'police'}
}
```

## Usage

### Opening the MDT
- **Command**: `/mdt` (configurable in config.lua)
- **Location**: Walk up to an MDT terminal at police stations
- **Target**: If using qb-target, interact with MDT objects

### Searching Citizens
1. Navigate to the "Citizen Search" tab
2. Enter a name or citizen ID
3. View results including criminal history, fines, and jail time

### Searching Vehicles
1. Navigate to the "Vehicle Search" tab
2. Enter a license plate (partial matches work)
3. View vehicle details and owner information

### Creating Reports
1. Navigate to the "Reports" tab
2. Click "Create New Report"
3. Fill in the report details:
   - Title and description
   - Suspects and witnesses
   - Evidence collected
4. Submit the report

### Managing BOLOs
1. Navigate to the "BOLOs" tab
2. View active BOLOs
3. Create new BOLOs for persons or vehicles

## Database Structure

The system uses several interconnected tables:

- `mdt_reports` - Incident reports
- `mdt_criminal_records` - Criminal charges and history
- `mdt_bolos` - Be On the Lookout alerts
- `mdt_evidence` - Evidence tracking
- `mdt_warrants` - Active warrants
- `mdt_officer_notes` - Officer notes on citizens
- `mdt_dispatch_calls` - Dispatch integration
- `mdt_vehicle_flags` - Vehicle flags and alerts
- `mdt_settings` - System configuration

## API Events

### Client Events
- `qb-mdt:client:OpenMDT` - Opens the MDT interface
- `qb-mdt:client:SetPlayerData` - Sets officer information
- `qb-mdt:client:SearchResult` - Receives search results
- `qb-mdt:client:ReceiveReports` - Receives report list

### Server Events
- `qb-mdt:server:GetPlayerData` - Gets officer data
- `qb-mdt:server:SearchCitizen` - Searches for citizens
- `qb-mdt:server:SearchVehicle` - Searches for vehicles
- `qb-mdt:server:CreateReport` - Creates a new report
- `qb-mdt:server:GetReports` - Gets report list

## Customization

### Adding New Charges
Edit the `Config.Charges` table in `config.lua`:
```lua
Config.Charges = {
    ['misdemeanor'] = {
        ['new_charge'] = {name = 'New Charge', fine = 500, time = 5}
    }
}
```

### Styling
Modify `html/css/style.css` to change the appearance:
- Colors and themes
- Layout and spacing
- Responsive breakpoints

### Adding Features
The modular structure allows for easy expansion:
- Add new tabs in `html/index.html`
- Implement functionality in `html/js/script.js`
- Add server-side logic in `server/main.lua`

## Support

For support, issues, or feature requests:
1. Check the configuration is correct
2. Verify all dependencies are installed
3. Check server console for errors
4. Review database permissions

## License

This resource is provided as-is for QBCore servers. Modify and distribute according to your server's needs.

## Credits

- Built for QBCore Framework
- Uses Font Awesome icons
- Responsive design with CSS Grid and Flexbox
