<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QB-MDT - Mobile Data Terminal</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="mdt-container" class="hidden">
        <!-- Header -->
        <div class="mdt-header">
            <div class="header-left">
                <i class="fas fa-shield-alt"></i>
                <h1>Mobile Data Terminal</h1>
            </div>
            <div class="header-right">
                <span id="officer-info">Officer: Loading...</span>
                <button id="close-btn" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Navigation -->
        <div class="mdt-nav">
            <button class="nav-btn active" data-tab="dashboard">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard
            </button>
            <button class="nav-btn" data-tab="citizen-search">
                <i class="fas fa-search"></i>
                Citizen Search
            </button>
            <button class="nav-btn" data-tab="vehicle-search">
                <i class="fas fa-car"></i>
                Vehicle Search
            </button>
            <button class="nav-btn" data-tab="reports">
                <i class="fas fa-file-alt"></i>
                Reports
            </button>
            <button class="nav-btn" data-tab="bolos">
                <i class="fas fa-exclamation-triangle"></i>
                BOLOs
            </button>
        </div>

        <!-- Content Area -->
        <div class="mdt-content">
            <!-- Dashboard Tab -->
            <div id="dashboard" class="tab-content active">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <i class="fas fa-users"></i>
                            <h3>Quick Actions</h3>
                        </div>
                        <div class="card-body">
                            <button class="action-btn" onclick="switchTab('citizen-search')">
                                <i class="fas fa-search"></i>
                                Search Citizens
                            </button>
                            <button class="action-btn" onclick="switchTab('vehicle-search')">
                                <i class="fas fa-car"></i>
                                Search Vehicles
                            </button>
                            <button class="action-btn" onclick="switchTab('reports')">
                                <i class="fas fa-plus"></i>
                                Create Report
                            </button>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-header">
                            <i class="fas fa-clock"></i>
                            <h3>Recent Activity</h3>
                        </div>
                        <div class="card-body">
                            <div id="recent-activity">
                                <p>No recent activity</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Citizen Search Tab -->
            <div id="citizen-search" class="tab-content">
                <div class="search-container">
                    <div class="search-header">
                        <h2>Citizen Search</h2>
                        <div class="search-box">
                            <input type="text" id="citizen-search-input" placeholder="Enter name or citizen ID...">
                            <button id="citizen-search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div id="citizen-results" class="results-container">
                        <p class="no-results">Enter a search term to find citizens</p>
                    </div>
                </div>
            </div>

            <!-- Vehicle Search Tab -->
            <div id="vehicle-search" class="tab-content">
                <div class="search-container">
                    <div class="search-header">
                        <h2>Vehicle Search</h2>
                        <div class="search-box">
                            <input type="text" id="vehicle-search-input" placeholder="Enter license plate...">
                            <button id="vehicle-search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div id="vehicle-results" class="results-container">
                        <p class="no-results">Enter a license plate to search vehicles</p>
                    </div>
                </div>
            </div>

            <!-- Reports Tab -->
            <div id="reports" class="tab-content">
                <div class="reports-container">
                    <div class="reports-header">
                        <h2>Reports</h2>
                        <button id="create-report-btn" class="primary-btn">
                            <i class="fas fa-plus"></i>
                            Create New Report
                        </button>
                    </div>
                    
                    <div id="reports-list" class="reports-list">
                        <p class="no-results">Loading reports...</p>
                    </div>
                </div>
            </div>

            <!-- BOLOs Tab -->
            <div id="bolos" class="tab-content">
                <div class="bolos-container">
                    <div class="bolos-header">
                        <h2>Be On the Lookout (BOLO)</h2>
                        <button id="create-bolo-btn" class="primary-btn">
                            <i class="fas fa-plus"></i>
                            Create BOLO
                        </button>
                    </div>
                    
                    <div id="bolos-list" class="bolos-list">
                        <p class="no-results">No active BOLOs</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Report Modal -->
    <div id="report-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Report</h3>
                <button class="close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="report-form">
                    <div class="form-group">
                        <label for="report-title">Title:</label>
                        <input type="text" id="report-title" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="report-description">Description:</label>
                        <textarea id="report-description" rows="6" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="report-suspects">Suspects (one per line):</label>
                        <textarea id="report-suspects" rows="3" placeholder="John Doe - Citizen ID: ABC123"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="report-witnesses">Witnesses (one per line):</label>
                        <textarea id="report-witnesses" rows="3" placeholder="Jane Smith - Citizen ID: DEF456"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="report-evidence">Evidence:</label>
                        <textarea id="report-evidence" rows="3" placeholder="List any evidence collected..."></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="secondary-btn close-modal">Cancel</button>
                        <button type="submit" class="primary-btn">Create Report</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
</body>
</html>
