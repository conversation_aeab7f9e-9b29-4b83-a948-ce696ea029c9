/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    overflow: hidden;
    margin: 0;
    padding: 0;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.hidden {
    display: none !important;
}

/* MDT Container */
#mdt-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90vw;
    height: 85vh;
    max-width: 1400px;
    max-height: 900px;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
    border-radius: 15px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.8);
    border: 2px solid #333333;
    display: flex;
    flex-direction: column;
    animation: slideIn 0.3s ease-out;
    backdrop-filter: blur(10px);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* Header */
.mdt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: linear-gradient(90deg, #111111 0%, #1a1a1a 100%);
    border-radius: 15px 15px 0 0;
    border-bottom: 2px solid #0066cc;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-left i {
    font-size: 24px;
    color: #0066cc;
}

.header-left h1 {
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

#officer-info {
    font-size: 14px;
    color: #cccccc;
    font-weight: 500;
}

.close-btn {
    background: #e74c3c;
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
}

/* Navigation */
.mdt-nav {
    display: flex;
    background: #0d0d0d;
    padding: 0 30px;
    border-bottom: 1px solid #333333;
}

.nav-btn {
    background: transparent;
    border: none;
    color: #999999;
    padding: 15px 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.nav-btn:hover {
    color: #0066cc;
    background: rgba(0, 102, 204, 0.1);
}

.nav-btn.active {
    color: #0066cc;
    border-bottom-color: #0066cc;
    background: rgba(0, 102, 204, 0.1);
}

/* Content Area */
.mdt-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.tab-content {
    display: none;
    height: 100%;
}

.tab-content.active {
    display: block;
}

/* Dashboard */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    height: 100%;
}

.dashboard-card {
    background: rgba(15, 15, 15, 0.9);
    border-radius: 10px;
    border: 1px solid #333333;
    overflow: hidden;
}

.card-header {
    background: #1a1a1a;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid #333333;
}

.card-header i {
    font-size: 20px;
    color: #0066cc;
}

.card-header h3 {
    font-size: 18px;
    font-weight: 600;
}

.card-body {
    padding: 25px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    padding: 15px 20px;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #0066cc 0%, #004499 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 102, 204, 0.4);
}

.action-btn:last-child {
    margin-bottom: 0;
}

/* Search Container */
.search-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.search-header {
    margin-bottom: 30px;
}

.search-header h2 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #0066cc;
}

.search-box {
    display: flex;
    gap: 10px;
    max-width: 500px;
}

.search-box input {
    flex: 1;
    padding: 12px 15px;
    background: rgba(15, 15, 15, 0.9);
    border: 2px solid #333333;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #0066cc;
}

.search-box input::placeholder {
    color: #666666;
}

.search-box button {
    padding: 12px 20px;
    background: #0066cc;
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-box button:hover {
    background: #004499;
}

/* Results Container */
.results-container {
    flex: 1;
    background: rgba(10, 10, 10, 0.8);
    border-radius: 10px;
    border: 1px solid #333333;
    padding: 25px;
    overflow-y: auto;
}

.no-results {
    text-align: center;
    color: #666666;
    font-style: italic;
    margin-top: 50px;
}

/* Result Cards */
.result-card {
    background: rgba(20, 20, 20, 0.9);
    border-radius: 8px;
    border: 1px solid #333333;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.result-card:hover {
    border-color: #0066cc;
    transform: translateY(-2px);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.result-name {
    font-size: 18px;
    font-weight: 600;
    color: #0066cc;
}

.result-id {
    font-size: 12px;
    color: #999999;
    background: rgba(51, 51, 51, 0.5);
    padding: 4px 8px;
    border-radius: 4px;
}

.result-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-label {
    font-size: 12px;
    color: #999999;
    text-transform: uppercase;
    font-weight: 600;
}

.detail-value {
    font-size: 14px;
    color: #ffffff;
}

/* Buttons */
.primary-btn {
    background: linear-gradient(135deg, #0066cc 0%, #004499 100%);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 102, 204, 0.4);
}

.secondary-btn {
    background: transparent;
    border: 2px solid #666666;
    color: #999999;
    padding: 10px 22px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.secondary-btn:hover {
    border-color: #999999;
    color: #cccccc;
}

/* Reports */
.reports-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.reports-header h2 {
    font-size: 24px;
    color: #0066cc;
}

.reports-list {
    flex: 1;
    background: rgba(10, 10, 10, 0.8);
    border-radius: 10px;
    border: 1px solid #333333;
    padding: 25px;
    overflow-y: auto;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
    border-radius: 15px;
    border: 2px solid #0066cc;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #333333;
}

.modal-header h3 {
    font-size: 20px;
    color: #0066cc;
}

.close-modal {
    background: #e74c3c;
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 30px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #cccccc;
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    background: rgba(15, 15, 15, 0.9);
    border: 2px solid #333333;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0066cc;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #666666;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
}

/* Charges Section */
.charges-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #34495e;
}

.charges-section h4 {
    color: #e74c3c;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 600;
}

.charges-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.charge-item {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
    border-radius: 6px;
    padding: 10px;
    font-size: 13px;
}

.charge-item strong {
    color: #e74c3c;
}

.charge-item small {
    color: #999999;
    display: block;
    margin-top: 5px;
}

/* BOLOs */
.bolos-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.bolos-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.bolos-header h2 {
    font-size: 24px;
    color: #e74c3c;
}

.bolos-list {
    flex: 1;
    background: rgba(10, 10, 10, 0.8);
    border-radius: 10px;
    border: 1px solid #333333;
    padding: 25px;
    overflow-y: auto;
}

.bolo-card {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.bolo-card:hover {
    border-color: #e74c3c;
    transform: translateY(-2px);
}

.bolo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.bolo-type {
    background: #e74c3c;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.bolo-date {
    font-size: 12px;
    color: #999999;
}

/* Status Badges */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background: #27ae60;
    color: white;
}

.status-inactive {
    background: #7f8c8d;
    color: white;
}

.status-pending {
    background: #f39c12;
    color: white;
}

.status-completed {
    background: #3498db;
    color: white;
}

/* Loading Animation */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 50px;
    color: #666666;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #666666;
    border-top: 2px solid #0066cc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    #mdt-container {
        width: 95vw;
        height: 90vh;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .result-details {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .mdt-nav {
        flex-wrap: wrap;
        padding: 0 15px;
    }

    .nav-btn {
        padding: 12px 15px;
        font-size: 12px;
    }

    .mdt-content {
        padding: 20px;
    }

    .search-box {
        flex-direction: column;
    }

    .form-actions {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(15, 15, 15, 0.8);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #0066cc;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #004499;
}
