/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    color: #ffffff;
    overflow: hidden;
}

.hidden {
    display: none !important;
}

/* MDT Container */
#mdt-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90vw;
    height: 85vh;
    max-width: 1400px;
    max-height: 900px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    border: 2px solid #2c3e50;
    display: flex;
    flex-direction: column;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* Header */
.mdt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: linear-gradient(90deg, #2c3e50 0%, #34495e 100%);
    border-radius: 15px 15px 0 0;
    border-bottom: 2px solid #3498db;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-left i {
    font-size: 24px;
    color: #3498db;
}

.header-left h1 {
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

#officer-info {
    font-size: 14px;
    color: #bdc3c7;
    font-weight: 500;
}

.close-btn {
    background: #e74c3c;
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
}

/* Navigation */
.mdt-nav {
    display: flex;
    background: #2c3e50;
    padding: 0 30px;
    border-bottom: 1px solid #34495e;
}

.nav-btn {
    background: transparent;
    border: none;
    color: #bdc3c7;
    padding: 15px 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.nav-btn:hover {
    color: #3498db;
    background: rgba(52, 152, 219, 0.1);
}

.nav-btn.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background: rgba(52, 152, 219, 0.1);
}

/* Content Area */
.mdt-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.tab-content {
    display: none;
    height: 100%;
}

.tab-content.active {
    display: block;
}

/* Dashboard */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    height: 100%;
}

.dashboard-card {
    background: rgba(44, 62, 80, 0.8);
    border-radius: 10px;
    border: 1px solid #34495e;
    overflow: hidden;
}

.card-header {
    background: #34495e;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid #2c3e50;
}

.card-header i {
    font-size: 20px;
    color: #3498db;
}

.card-header h3 {
    font-size: 18px;
    font-weight: 600;
}

.card-body {
    padding: 25px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    padding: 15px 20px;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.action-btn:last-child {
    margin-bottom: 0;
}

/* Search Container */
.search-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.search-header {
    margin-bottom: 30px;
}

.search-header h2 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #3498db;
}

.search-box {
    display: flex;
    gap: 10px;
    max-width: 500px;
}

.search-box input {
    flex: 1;
    padding: 12px 15px;
    background: rgba(44, 62, 80, 0.8);
    border: 2px solid #34495e;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
}

.search-box input::placeholder {
    color: #7f8c8d;
}

.search-box button {
    padding: 12px 20px;
    background: #3498db;
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-box button:hover {
    background: #2980b9;
}

/* Results Container */
.results-container {
    flex: 1;
    background: rgba(44, 62, 80, 0.5);
    border-radius: 10px;
    border: 1px solid #34495e;
    padding: 25px;
    overflow-y: auto;
}

.no-results {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    margin-top: 50px;
}

/* Result Cards */
.result-card {
    background: rgba(52, 73, 94, 0.8);
    border-radius: 8px;
    border: 1px solid #34495e;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.result-card:hover {
    border-color: #3498db;
    transform: translateY(-2px);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.result-name {
    font-size: 18px;
    font-weight: 600;
    color: #3498db;
}

.result-id {
    font-size: 12px;
    color: #7f8c8d;
    background: rgba(127, 140, 141, 0.2);
    padding: 4px 8px;
    border-radius: 4px;
}

.result-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-label {
    font-size: 12px;
    color: #7f8c8d;
    text-transform: uppercase;
    font-weight: 600;
}

.detail-value {
    font-size: 14px;
    color: #ecf0f1;
}

/* Buttons */
.primary-btn {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.secondary-btn {
    background: transparent;
    border: 2px solid #7f8c8d;
    color: #7f8c8d;
    padding: 10px 22px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.secondary-btn:hover {
    border-color: #bdc3c7;
    color: #bdc3c7;
}

/* Reports */
.reports-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.reports-header h2 {
    font-size: 24px;
    color: #3498db;
}

.reports-list {
    flex: 1;
    background: rgba(44, 62, 80, 0.5);
    border-radius: 10px;
    border: 1px solid #34495e;
    padding: 25px;
    overflow-y: auto;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: 15px;
    border: 2px solid #3498db;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #34495e;
}

.modal-header h3 {
    font-size: 20px;
    color: #3498db;
}

.close-modal {
    background: #e74c3c;
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 30px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #bdc3c7;
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    background: rgba(44, 62, 80, 0.8);
    border: 2px solid #34495e;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #7f8c8d;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
}

/* Charges Section */
.charges-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #34495e;
}

.charges-section h4 {
    color: #e74c3c;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 600;
}

.charges-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.charge-item {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
    border-radius: 6px;
    padding: 10px;
    font-size: 13px;
}

.charge-item strong {
    color: #e74c3c;
}

.charge-item small {
    color: #7f8c8d;
    display: block;
    margin-top: 5px;
}

/* BOLOs */
.bolos-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.bolos-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.bolos-header h2 {
    font-size: 24px;
    color: #e74c3c;
}

.bolos-list {
    flex: 1;
    background: rgba(44, 62, 80, 0.5);
    border-radius: 10px;
    border: 1px solid #34495e;
    padding: 25px;
    overflow-y: auto;
}

.bolo-card {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.bolo-card:hover {
    border-color: #e74c3c;
    transform: translateY(-2px);
}

.bolo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.bolo-type {
    background: #e74c3c;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.bolo-date {
    font-size: 12px;
    color: #7f8c8d;
}

/* Status Badges */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background: #27ae60;
    color: white;
}

.status-inactive {
    background: #7f8c8d;
    color: white;
}

.status-pending {
    background: #f39c12;
    color: white;
}

.status-completed {
    background: #3498db;
    color: white;
}

/* Loading Animation */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 50px;
    color: #7f8c8d;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #7f8c8d;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    #mdt-container {
        width: 95vw;
        height: 90vh;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .result-details {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .mdt-nav {
        flex-wrap: wrap;
        padding: 0 15px;
    }

    .nav-btn {
        padding: 12px 15px;
        font-size: 12px;
    }

    .mdt-content {
        padding: 20px;
    }

    .search-box {
        flex-direction: column;
    }

    .form-actions {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(44, 62, 80, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #3498db;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2980b9;
}
