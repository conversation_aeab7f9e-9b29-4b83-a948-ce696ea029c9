// Global Variables
let playerData = {};
let currentCharges = {};

// DOM Elements
const mdtContainer = document.getElementById('mdt-container');
const officerInfo = document.getElementById('officer-info');
const closeBtn = document.getElementById('close-btn');
const navBtns = document.querySelectorAll('.nav-btn');
const tabContents = document.querySelectorAll('.tab-content');

// Search Elements
const citizenSearchInput = document.getElementById('citizen-search-input');
const citizenSearchBtn = document.getElementById('citizen-search-btn');
const citizenResults = document.getElementById('citizen-results');

const vehicleSearchInput = document.getElementById('vehicle-search-input');
const vehicleSearchBtn = document.getElementById('vehicle-search-btn');
const vehicleResults = document.getElementById('vehicle-results');

// Report Elements
const createReportBtn = document.getElementById('create-report-btn');
const reportModal = document.getElementById('report-modal');
const reportForm = document.getElementById('report-form');
const reportsList = document.getElementById('reports-list');
const closeModalBtns = document.querySelectorAll('.close-modal');

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

function initializeEventListeners() {
    // Close button
    closeBtn.addEventListener('click', closeMDT);
    
    // Navigation
    navBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            switchTab(tabName);
        });
    });
    
    // Search functionality
    citizenSearchBtn.addEventListener('click', searchCitizen);
    citizenSearchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') searchCitizen();
    });
    
    vehicleSearchBtn.addEventListener('click', searchVehicle);
    vehicleSearchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') searchVehicle();
    });
    
    // Report functionality
    createReportBtn.addEventListener('click', openReportModal);
    reportForm.addEventListener('submit', createReport);
    
    // Modal close
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', closeModal);
    });
    
    // Close modal on background click
    reportModal.addEventListener('click', function(e) {
        if (e.target === reportModal) closeModal();
    });
    
    // ESC key to close
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            if (!reportModal.classList.contains('hidden')) {
                closeModal();
            } else {
                closeMDT();
            }
        }
    });
}

// NUI Message Handler
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'openMDT':
            openMDT();
            break;
        case 'setPlayerData':
            setPlayerData(data.data);
            break;
        case 'searchResult':
            handleSearchResult(data.type, data.results);
            break;
        case 'receiveReports':
            displayReports(data.reports);
            break;
        case 'reportCreated':
            handleReportCreated(data.reportId);
            break;
    }
});

// Core Functions
function openMDT() {
    mdtContainer.classList.remove('hidden');
    loadReports();
    getCharges();
}

function closeMDT() {
    mdtContainer.classList.add('hidden');
    fetch(`https://${GetParentResourceName()}/closeMDT`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

function setPlayerData(data) {
    playerData = data;
    officerInfo.textContent = `Officer: ${data.name} | Callsign: ${data.callsign}`;
}

function switchTab(tabName) {
    // Update navigation
    navBtns.forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-tab') === tabName) {
            btn.classList.add('active');
        }
    });
    
    // Update content
    tabContents.forEach(content => {
        content.classList.remove('active');
        if (content.id === tabName) {
            content.classList.add('active');
        }
    });
    
    // Load data for specific tabs
    if (tabName === 'reports') {
        loadReports();
    }
}

// Search Functions
function searchCitizen() {
    const searchTerm = citizenSearchInput.value.trim();
    if (!searchTerm) return;
    
    citizenResults.innerHTML = '<p class="no-results">Searching...</p>';
    
    fetch(`https://${GetParentResourceName()}/searchCitizen`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            searchTerm: searchTerm
        })
    });
}

function searchVehicle() {
    const plate = vehicleSearchInput.value.trim();
    if (!plate) return;
    
    vehicleResults.innerHTML = '<p class="no-results">Searching...</p>';
    
    fetch(`https://${GetParentResourceName()}/searchVehicle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            plate: plate
        })
    });
}

function handleSearchResult(type, results) {
    if (type === 'citizen') {
        displayCitizenResults(results);
    } else if (type === 'vehicle') {
        displayVehicleResults(results);
    }
}

function displayCitizenResults(results) {
    if (!results || results.length === 0) {
        citizenResults.innerHTML = '<p class="no-results">No citizens found</p>';
        return;
    }
    
    let html = '';
    results.forEach(citizen => {
        html += `
            <div class="result-card">
                <div class="result-header">
                    <div class="result-name">${citizen.firstname} ${citizen.lastname}</div>
                    <div class="result-id">ID: ${citizen.citizenid}</div>
                </div>
                <div class="result-details">
                    <div class="detail-item">
                        <div class="detail-label">Date of Birth</div>
                        <div class="detail-value">${citizen.dob || 'Unknown'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Phone Number</div>
                        <div class="detail-value">${citizen.phone || 'Unknown'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Total Fines</div>
                        <div class="detail-value">$${citizen.total_fine || 0}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Total Jail Time</div>
                        <div class="detail-value">${citizen.total_time || 0} months</div>
                    </div>
                </div>
                ${citizen.charges && citizen.charges.length > 0 ? `
                    <div class="charges-section">
                        <h4>Criminal Record:</h4>
                        <div class="charges-list">
                            ${citizen.charges.map(charge => `
                                <div class="charge-item">
                                    <strong>${charge.charge}</strong> - $${charge.fine} - ${charge.time} months
                                    <small>(${new Date(charge.date).toLocaleDateString()})</small>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    });
    
    citizenResults.innerHTML = html;
}

function displayVehicleResults(results) {
    if (!results || results.length === 0) {
        vehicleResults.innerHTML = '<p class="no-results">No vehicles found</p>';
        return;
    }
    
    let html = '';
    results.forEach(vehicle => {
        html += `
            <div class="result-card">
                <div class="result-header">
                    <div class="result-name">${vehicle.plate}</div>
                    <div class="result-id">Owner: ${vehicle.owner}</div>
                </div>
                <div class="result-details">
                    <div class="detail-item">
                        <div class="detail-label">Make/Model</div>
                        <div class="detail-value">${vehicle.brand} ${vehicle.model}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Color</div>
                        <div class="detail-value">${vehicle.color}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Garage</div>
                        <div class="detail-value">${vehicle.garage || 'Unknown'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Owner ID</div>
                        <div class="detail-value">${vehicle.citizenid}</div>
                    </div>
                </div>
            </div>
        `;
    });
    
    vehicleResults.innerHTML = html;
}

// Report Functions
function openReportModal() {
    reportModal.classList.remove('hidden');
}

function closeModal() {
    reportModal.classList.add('hidden');
    reportForm.reset();
}

function createReport(e) {
    e.preventDefault();
    
    const title = document.getElementById('report-title').value;
    const description = document.getElementById('report-description').value;
    const suspects = document.getElementById('report-suspects').value;
    const witnesses = document.getElementById('report-witnesses').value;
    const evidence = document.getElementById('report-evidence').value;
    
    const reportData = {
        title: title,
        description: description,
        suspects: suspects.split('\n').filter(s => s.trim()),
        witnesses: witnesses.split('\n').filter(w => w.trim()),
        evidence: evidence.split('\n').filter(e => e.trim())
    };
    
    fetch(`https://${GetParentResourceName()}/createReport`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportData)
    });
    
    closeModal();
}

function loadReports() {
    fetch(`https://${GetParentResourceName()}/getReports`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

function displayReports(reports) {
    if (!reports || reports.length === 0) {
        reportsList.innerHTML = '<p class="no-results">No reports found</p>';
        return;
    }
    
    let html = '';
    reports.forEach(report => {
        html += `
            <div class="result-card">
                <div class="result-header">
                    <div class="result-name">${report.title}</div>
                    <div class="result-id">ID: ${report.id}</div>
                </div>
                <div class="result-details">
                    <div class="detail-item">
                        <div class="detail-label">Officer</div>
                        <div class="detail-value">${report.officer_name}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Date</div>
                        <div class="detail-value">${new Date(report.date).toLocaleDateString()}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Preview</div>
                        <div class="detail-value">${report.preview}...</div>
                    </div>
                </div>
            </div>
        `;
    });
    
    reportsList.innerHTML = html;
}

function handleReportCreated(reportId) {
    loadReports();
}

function getCharges() {
    fetch(`https://${GetParentResourceName()}/getCharges`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    }).then(response => response.json())
    .then(data => {
        currentCharges = data;
    });
}

// Utility Functions
function GetParentResourceName() {
    return window.location.hostname;
}
