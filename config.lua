Config = {}

-- General Settings
Config.Debug = false
Config.UseTarget = false -- Set to true if using qb-target
Config.MDTCommand = 'mdt' -- Command to open MDT
Config.PoliceJobs = {
    'police',
    'sheriff',
    'statepolice'
}

-- Database Settings
Config.UseOxMySQL = true

-- MDT Access Locations
Config.MDTLocations = {
    -- Mission Row PD
    {
        coords = vector3(441.07, -979.56, 30.69),
        heading = 180.0,
        width = 2.0,
        height = 2.0,
        minZ = 29.69,
        maxZ = 31.69
    },
    -- Paleto Bay Sheriff
    {
        coords = vector3(-448.11, 6008.54, 31.72),
        heading = 315.0,
        width = 2.0,
        height = 2.0,
        minZ = 30.72,
        maxZ = 32.72
    },
    -- <PERSON> Sheriff
    {
        coords = vector3(1853.91, 3689.51, 34.27),
        heading = 210.0,
        width = 2.0,
        height = 2.0,
        minZ = 33.27,
        maxZ = 35.27
    }
}

-- Permissions
Config.Permissions = {
    ['citizen_search'] = {'police', 'sheriff', 'statepolice'},
    ['vehicle_search'] = {'police', 'sheriff', 'statepolice'},
    ['create_report'] = {'police', 'sheriff', 'statepolice'},
    ['view_reports'] = {'police', 'sheriff', 'statepolice'},
    ['create_bolo'] = {'police', 'sheriff', 'statepolice'},
    ['admin_panel'] = {'police'} -- Only police can access admin features
}

-- Charges
Config.Charges = {
    ['misdemeanor'] = {
        ['disturbing_peace'] = {name = 'Disturbing the Peace', fine = 500, time = 0},
        ['public_intoxication'] = {name = 'Public Intoxication', fine = 300, time = 0},
        ['trespassing'] = {name = 'Trespassing', fine = 400, time = 5},
        ['vandalism'] = {name = 'Vandalism', fine = 600, time = 10}
    },
    ['felony'] = {
        ['assault'] = {name = 'Assault', fine = 1000, time = 15},
        ['robbery'] = {name = 'Robbery', fine = 2000, time = 30},
        ['drug_possession'] = {name = 'Drug Possession', fine = 1500, time = 20},
        ['weapons_violation'] = {name = 'Weapons Violation', fine = 2500, time = 25}
    },
    ['traffic'] = {
        ['speeding'] = {name = 'Speeding', fine = 200, time = 0},
        ['reckless_driving'] = {name = 'Reckless Driving', fine = 500, time = 0},
        ['dui'] = {name = 'Driving Under Influence', fine = 1000, time = 10},
        ['hit_and_run'] = {name = 'Hit and Run', fine = 1500, time = 15}
    }
}

-- Evidence Types
Config.EvidenceTypes = {
    'DNA',
    'Fingerprint',
    'Blood',
    'Photo',
    'Video',
    'Audio',
    'Document',
    'Physical Evidence'
}
