local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = {}
local mdtOpen = false

-- Events
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
end)

RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
    PlayerData.job = JobInfo
end)

RegisterNetEvent('qb-mdt:client:OpenMDT', function()
    if mdtOpen then return end
    
    -- Check if player is in a police job
    local hasAccess = false
    for i = 1, #Config.PoliceJobs do
        if PlayerData.job.name == Config.PoliceJobs[i] then
            hasAccess = true
            break
        end
    end
    
    if not hasAccess then
        QBCore.Functions.Notify('You do not have access to the MDT', 'error')
        return
    end
    
    mdtOpen = true
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = 'openMDT'
    })
    
    -- Get player data for MDT
    TriggerServerEvent('qb-mdt:server:GetPlayerData')
end)

RegisterNetEvent('qb-mdt:client:SetPlayerData', function(data)
    SendNUIMessage({
        action = 'setPlayerData',
        data = data
    })
end)

RegisterNetEvent('qb-mdt:client:SearchResult', function(type, results)
    SendNUIMessage({
        action = 'searchResult',
        type = type,
        results = results
    })
end)

RegisterNetEvent('qb-mdt:client:ReceiveReports', function(reports)
    SendNUIMessage({
        action = 'receiveReports',
        reports = reports
    })
end)

RegisterNetEvent('qb-mdt:client:ReportCreated', function(reportId)
    SendNUIMessage({
        action = 'reportCreated',
        reportId = reportId
    })
end)

-- NUI Callbacks
RegisterNUICallback('closeMDT', function(data, cb)
    mdtOpen = false
    SetNuiFocus(false, false)
    cb('ok')
end)

RegisterNUICallback('searchCitizen', function(data, cb)
    if data.searchTerm and data.searchTerm ~= '' then
        TriggerServerEvent('qb-mdt:server:SearchCitizen', data.searchTerm)
    end
    cb('ok')
end)

RegisterNUICallback('searchVehicle', function(data, cb)
    if data.plate and data.plate ~= '' then
        TriggerServerEvent('qb-mdt:server:SearchVehicle', data.plate)
    end
    cb('ok')
end)

RegisterNUICallback('createReport', function(data, cb)
    TriggerServerEvent('qb-mdt:server:CreateReport', data)
    cb('ok')
end)

RegisterNUICallback('getReports', function(data, cb)
    TriggerServerEvent('qb-mdt:server:GetReports')
    cb('ok')
end)

RegisterNUICallback('getCharges', function(data, cb)
    cb(Config.Charges)
end)

-- Commands
RegisterCommand(Config.MDTCommand, function()
    TriggerEvent('qb-mdt:client:OpenMDT')
end)

-- Target Integration (if enabled)
if Config.UseTarget then
    CreateThread(function()
        for i = 1, #Config.MDTLocations do
            local location = Config.MDTLocations[i]
            exports['qb-target']:AddBoxZone('mdt_' .. i, location.coords, location.width, location.height, {
                name = 'mdt_' .. i,
                heading = location.heading,
                minZ = location.minZ,
                maxZ = location.maxZ
            }, {
                options = {
                    {
                        type = 'client',
                        event = 'qb-mdt:client:OpenMDT',
                        icon = 'fas fa-laptop',
                        label = 'Access MDT',
                        job = Config.PoliceJobs
                    }
                },
                distance = 2.0
            })
        end
    end)
else
    -- Draw text and key press for MDT locations
    CreateThread(function()
        while true do
            local sleep = 1000
            local ped = PlayerPedId()
            local pos = GetEntityCoords(ped)
            
            for i = 1, #Config.MDTLocations do
                local location = Config.MDTLocations[i]
                local distance = #(pos - location.coords)
                
                if distance < 3.0 then
                    sleep = 0
                    DrawText3D(location.coords.x, location.coords.y, location.coords.z + 0.5, '[E] Access MDT')
                    
                    if distance < 1.5 and IsControlJustPressed(0, 38) then -- E key
                        TriggerEvent('qb-mdt:client:OpenMDT')
                    end
                end
            end
            
            Wait(sleep)
        end
    end)
end

-- Helper Functions
function DrawText3D(x, y, z, text)
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry('STRING')
    SetTextCentre(true)
    AddTextComponentString(text)
    SetDrawOrigin(x, y, z, 0)
    DrawText(0.0, 0.0)
    local factor = (string.len(text)) / 370
    DrawRect(0.0, 0.0+0.0125, 0.017+ factor, 0.03, 0, 0, 0, 75)
    ClearDrawOrigin()
end

-- Cleanup
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        if mdtOpen then
            SetNuiFocus(false, false)
        end
    end
end)
