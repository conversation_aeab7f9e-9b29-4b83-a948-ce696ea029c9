-- QB-MDT Database Structure
-- Execute these queries in your database to set up the MDT system

-- Reports Table
CREATE TABLE IF NOT EXISTS `mdt_reports` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `title` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `officer_name` VARCHA<PERSON>(100),
    `officer_citizenid` VARCHAR(50),
    `date` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `evidence` JSO<PERSON>,
    `suspects` JSO<PERSON>,
    `witnesses` JSON,
    `status` ENUM('active', 'closed', 'archived') DEFAULT 'active',
    INDEX `idx_officer` (`officer_citizenid`),
    INDEX `idx_date` (`date`),
    INDEX `idx_status` (`status`)
);

-- Criminal Records Table
CREATE TABLE IF NOT EXISTS `mdt_criminal_records` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `citizenid` VARCHAR(50) NOT NULL,
    `charge` VARCHAR(255) NOT NULL,
    `fine` INT DEFAULT 0,
    `time` INT DEFAULT 0,
    `officer` <PERSON><PERSON><PERSON><PERSON>(100),
    `officer_citizenid` VARCHAR(50),
    `date` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `report_id` INT,
    INDEX `idx_citizenid` (`citizenid`),
    INDEX `idx_officer` (`officer_citizenid`),
    INDEX `idx_date` (`date`),
    FOREIGN KEY (`report_id`) REFERENCES `mdt_reports`(`id`) ON DELETE SET NULL
);

-- BOLOs (Be On the Lookout) Table
CREATE TABLE IF NOT EXISTS `mdt_bolos` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `type` ENUM('person', 'vehicle') NOT NULL,
    `title` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `details` JSON,
    `officer` VARCHAR(100),
    `officer_citizenid` VARCHAR(50),
    `date` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `active` BOOLEAN DEFAULT TRUE,
    `expires` DATETIME NULL,
    INDEX `idx_type` (`type`),
    INDEX `idx_active` (`active`),
    INDEX `idx_officer` (`officer_citizenid`),
    INDEX `idx_date` (`date`)
);

-- Evidence Table
CREATE TABLE IF NOT EXISTS `mdt_evidence` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `report_id` INT NOT NULL,
    `type` VARCHAR(50) NOT NULL,
    `description` TEXT,
    `location` VARCHAR(255),
    `collected_by` VARCHAR(100),
    `collected_by_citizenid` VARCHAR(50),
    `date_collected` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `image_url` VARCHAR(500),
    `metadata` JSON,
    INDEX `idx_report` (`report_id`),
    INDEX `idx_type` (`type`),
    INDEX `idx_collected_by` (`collected_by_citizenid`),
    FOREIGN KEY (`report_id`) REFERENCES `mdt_reports`(`id`) ON DELETE CASCADE
);

-- Warrants Table
CREATE TABLE IF NOT EXISTS `mdt_warrants` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `citizenid` VARCHAR(50) NOT NULL,
    `title` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `charges` JSON,
    `issued_by` VARCHAR(100),
    `issued_by_citizenid` VARCHAR(50),
    `date_issued` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `active` BOOLEAN DEFAULT TRUE,
    `executed` BOOLEAN DEFAULT FALSE,
    `executed_by` VARCHAR(100),
    `executed_by_citizenid` VARCHAR(50),
    `date_executed` DATETIME NULL,
    INDEX `idx_citizenid` (`citizenid`),
    INDEX `idx_active` (`active`),
    INDEX `idx_issued_by` (`issued_by_citizenid`),
    INDEX `idx_date_issued` (`date_issued`)
);

-- Officer Notes Table
CREATE TABLE IF NOT EXISTS `mdt_officer_notes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `citizenid` VARCHAR(50) NOT NULL,
    `note` TEXT NOT NULL,
    `officer` VARCHAR(100),
    `officer_citizenid` VARCHAR(50),
    `date` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `private` BOOLEAN DEFAULT FALSE,
    INDEX `idx_citizenid` (`citizenid`),
    INDEX `idx_officer` (`officer_citizenid`),
    INDEX `idx_date` (`date`)
);

-- Dispatch Calls Table
CREATE TABLE IF NOT EXISTS `mdt_dispatch_calls` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `call_id` VARCHAR(50) UNIQUE,
    `title` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `location` VARCHAR(255),
    `coordinates` JSON,
    `priority` ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    `status` ENUM('pending', 'assigned', 'en_route', 'on_scene', 'completed', 'cancelled') DEFAULT 'pending',
    `caller_name` VARCHAR(100),
    `caller_phone` VARCHAR(20),
    `assigned_officers` JSON,
    `created_date` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_date` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_status` (`status`),
    INDEX `idx_priority` (`priority`),
    INDEX `idx_created_date` (`created_date`)
);

-- Vehicle Flags Table
CREATE TABLE IF NOT EXISTS `mdt_vehicle_flags` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `plate` VARCHAR(20) NOT NULL,
    `flag_type` ENUM('stolen', 'wanted', 'impounded', 'evidence', 'other') NOT NULL,
    `description` TEXT,
    `officer` VARCHAR(100),
    `officer_citizenid` VARCHAR(50),
    `date_flagged` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `active` BOOLEAN DEFAULT TRUE,
    INDEX `idx_plate` (`plate`),
    INDEX `idx_flag_type` (`flag_type`),
    INDEX `idx_active` (`active`),
    INDEX `idx_officer` (`officer_citizenid`)
);

-- MDT Settings Table
CREATE TABLE IF NOT EXISTS `mdt_settings` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `setting_key` VARCHAR(100) UNIQUE NOT NULL,
    `setting_value` TEXT,
    `description` TEXT,
    `updated_by` VARCHAR(50),
    `updated_date` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default settings
INSERT IGNORE INTO `mdt_settings` (`setting_key`, `setting_value`, `description`) VALUES
('auto_backup_reports', 'true', 'Automatically backup reports daily'),
('max_search_results', '50', 'Maximum number of search results to display'),
('evidence_retention_days', '365', 'Number of days to retain evidence records'),
('bolo_default_expiry_hours', '72', 'Default BOLO expiry time in hours'),
('require_supervisor_approval', 'false', 'Require supervisor approval for certain actions');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_players_name` ON `players` (JSON_EXTRACT(`charinfo`, '$.firstname'), JSON_EXTRACT(`charinfo`, '$.lastname'));
CREATE INDEX IF NOT EXISTS `idx_player_vehicles_plate` ON `player_vehicles` (`plate`);

-- Create views for common queries
CREATE OR REPLACE VIEW `mdt_citizen_summary` AS
SELECT 
    p.citizenid,
    JSON_UNQUOTE(JSON_EXTRACT(p.charinfo, '$.firstname')) as firstname,
    JSON_UNQUOTE(JSON_EXTRACT(p.charinfo, '$.lastname')) as lastname,
    JSON_UNQUOTE(JSON_EXTRACT(p.charinfo, '$.birthdate')) as birthdate,
    JSON_UNQUOTE(JSON_EXTRACT(p.charinfo, '$.phone')) as phone,
    COALESCE(cr.total_charges, 0) as total_charges,
    COALESCE(cr.total_fine, 0) as total_fine,
    COALESCE(cr.total_time, 0) as total_time,
    COALESCE(w.active_warrants, 0) as active_warrants
FROM players p
LEFT JOIN (
    SELECT 
        citizenid,
        COUNT(*) as total_charges,
        SUM(fine) as total_fine,
        SUM(time) as total_time
    FROM mdt_criminal_records
    GROUP BY citizenid
) cr ON p.citizenid = cr.citizenid
LEFT JOIN (
    SELECT 
        citizenid,
        COUNT(*) as active_warrants
    FROM mdt_warrants
    WHERE active = TRUE AND executed = FALSE
    GROUP BY citizenid
) w ON p.citizenid = w.citizenid;
