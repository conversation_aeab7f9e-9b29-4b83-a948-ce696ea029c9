local QBCore = exports['qb-core']:GetCoreObject()

-- Events
RegisterNetEvent('qb-mdt:server:GetPlayerData', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local playerData = {
        citizenid = Player.PlayerData.citizenid,
        name = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname,
        job = Player.PlayerData.job.name,
        grade = Player.PlayerData.job.grade.level,
        callsign = Player.PlayerData.metadata.callsign or 'N/A'
    }
    
    TriggerClientEvent('qb-mdt:client:SetPlayerData', src, playerData)
end)

RegisterNetEvent('qb-mdt:server:SearchCitizen', function(searchTerm)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player or not HasPermission(Player.PlayerData.job.name, 'citizen_search') then return end
    
    local query = [[
        SELECT p.citizenid, p.charinfo, p.metadata, 
               COALESCE(cr.charges, '[]') as charges,
               COALESCE(cr.total_fine, 0) as total_fine,
               COALESCE(cr.total_time, 0) as total_time
        FROM players p
        LEFT JOIN (
            SELECT citizenid, 
                   JSON_ARRAYAGG(JSON_OBJECT('charge', charge, 'fine', fine, 'time', time, 'date', date)) as charges,
                   SUM(fine) as total_fine,
                   SUM(time) as total_time
            FROM mdt_criminal_records 
            GROUP BY citizenid
        ) cr ON p.citizenid = cr.citizenid
        WHERE JSON_UNQUOTE(JSON_EXTRACT(p.charinfo, '$.firstname')) LIKE ? 
           OR JSON_UNQUOTE(JSON_EXTRACT(p.charinfo, '$.lastname')) LIKE ?
           OR p.citizenid LIKE ?
        LIMIT 20
    ]]
    
    local searchPattern = '%' .. searchTerm .. '%'
    MySQL.query(query, {searchPattern, searchPattern, searchPattern}, function(result)
        if result and #result > 0 then
            local citizens = {}
            for i = 1, #result do
                local row = result[i]
                local charinfo = json.decode(row.charinfo)
                local charges = json.decode(row.charges or '[]')
                
                citizens[#citizens + 1] = {
                    citizenid = row.citizenid,
                    firstname = charinfo.firstname,
                    lastname = charinfo.lastname,
                    dob = charinfo.birthdate,
                    phone = charinfo.phone,
                    charges = charges,
                    total_fine = row.total_fine,
                    total_time = row.total_time
                }
            end
            TriggerClientEvent('qb-mdt:client:SearchResult', src, 'citizen', citizens)
        else
            TriggerClientEvent('qb-mdt:client:SearchResult', src, 'citizen', {})
        end
    end)
end)

RegisterNetEvent('qb-mdt:server:SearchVehicle', function(plate)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player or not HasPermission(Player.PlayerData.job.name, 'vehicle_search') then return end
    
    local query = [[
        SELECT v.*, p.charinfo 
        FROM player_vehicles v
        LEFT JOIN players p ON v.citizenid = p.citizenid
        WHERE v.plate LIKE ?
        LIMIT 10
    ]]
    
    MySQL.query(query, {'%' .. plate .. '%'}, function(result)
        if result and #result > 0 then
            local vehicles = {}
            for i = 1, #result do
                local row = result[i]
                local charinfo = json.decode(row.charinfo or '{}')
                local vehicle = json.decode(row.vehicle or '{}')
                
                vehicles[#vehicles + 1] = {
                    plate = row.plate,
                    citizenid = row.citizenid,
                    owner = charinfo.firstname and (charinfo.firstname .. ' ' .. charinfo.lastname) or 'Unknown',
                    model = vehicle.model or 'Unknown',
                    brand = vehicle.brand or 'Unknown',
                    color = vehicle.color or 'Unknown',
                    garage = row.garage
                }
            end
            TriggerClientEvent('qb-mdt:client:SearchResult', src, 'vehicle', vehicles)
        else
            TriggerClientEvent('qb-mdt:client:SearchResult', src, 'vehicle', {})
        end
    end)
end)

RegisterNetEvent('qb-mdt:server:CreateReport', function(reportData)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player or not HasPermission(Player.PlayerData.job.name, 'create_report') then return end
    
    local query = [[
        INSERT INTO mdt_reports (title, description, officer_name, officer_citizenid, date, evidence, suspects, witnesses)
        VALUES (?, ?, ?, ?, NOW(), ?, ?, ?)
    ]]
    
    local officerName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
    
    MySQL.insert(query, {
        reportData.title,
        reportData.description,
        officerName,
        Player.PlayerData.citizenid,
        json.encode(reportData.evidence or {}),
        json.encode(reportData.suspects or {}),
        json.encode(reportData.witnesses or {})
    }, function(insertId)
        if insertId then
            TriggerClientEvent('QBCore:Notify', src, 'Report created successfully', 'success')
            TriggerClientEvent('qb-mdt:client:ReportCreated', src, insertId)
        else
            TriggerClientEvent('QBCore:Notify', src, 'Failed to create report', 'error')
        end
    end)
end)

RegisterNetEvent('qb-mdt:server:GetReports', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player or not HasPermission(Player.PlayerData.job.name, 'view_reports') then return end
    
    local query = [[
        SELECT id, title, officer_name, date, 
               SUBSTRING(description, 1, 100) as preview
        FROM mdt_reports 
        ORDER BY date DESC 
        LIMIT 50
    ]]
    
    MySQL.query(query, {}, function(result)
        TriggerClientEvent('qb-mdt:client:ReceiveReports', src, result or {})
    end)
end)

-- Helper Functions
function HasPermission(job, permission)
    if not Config.Permissions[permission] then return false end
    for i = 1, #Config.Permissions[permission] do
        if Config.Permissions[permission][i] == job then
            return true
        end
    end
    return false
end

-- Commands
QBCore.Commands.Add(Config.MDTCommand, 'Open Mobile Data Terminal', {}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    local hasAccess = false
    for i = 1, #Config.PoliceJobs do
        if Player.PlayerData.job.name == Config.PoliceJobs[i] then
            hasAccess = true
            break
        end
    end
    
    if hasAccess then
        TriggerClientEvent('qb-mdt:client:OpenMDT', source)
    else
        TriggerClientEvent('QBCore:Notify', source, 'You do not have access to the MDT', 'error')
    end
end)

-- Startup
CreateThread(function()
    -- Create database tables if they don't exist
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS mdt_reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            officer_name VARCHAR(100),
            officer_citizenid VARCHAR(50),
            date DATETIME DEFAULT CURRENT_TIMESTAMP,
            evidence JSON,
            suspects JSON,
            witnesses JSON
        )
    ]])
    
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS mdt_criminal_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            citizenid VARCHAR(50),
            charge VARCHAR(255),
            fine INT DEFAULT 0,
            time INT DEFAULT 0,
            officer VARCHAR(100),
            date DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ]])
    
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS mdt_bolos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type ENUM('person', 'vehicle') NOT NULL,
            title VARCHAR(255),
            description TEXT,
            officer VARCHAR(100),
            date DATETIME DEFAULT CURRENT_TIMESTAMP,
            active BOOLEAN DEFAULT TRUE
        )
    ]])
    
    print('^2[QB-MDT]^7 Database tables created/verified')
end)
